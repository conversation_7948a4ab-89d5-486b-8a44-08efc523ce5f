/**
 * maptile_chargify.mc_type Documentation
 * =====================================
 *
 * The mc_type field represents different subscription/account types:
 *
 * mc_type = '0' - Regular Paid Member
 * - Represents a regular paying subscription/member account
 * - When mc_status = '1' and mc_type = '0', user gets 'member' status
 * - Subject to billing cycles and payment due dates
 * - Can have mc_pastdue = '1' (past due payments)
 * - Can have mc_status = '0' (inactive subscription)
 * - No specific site limits enforced by type (limits come from subscription plan)
 *
 * mc_type = '1' - Trial Account
 * - Represents a trial subscription
 * - When mc_status = '1' and mc_type = '1', user gets 'trial' status
 * - Has a hard limit of 5 sites maximum (mc_max >= 5 triggers "max" error)
 * - Used for users testing the service before committing to paid plans
 * - Limited functionality compared to full members
 *
 * mc_type = '2' - Special Member/Enterprise
 * - Represents a special member account (likely enterprise or custom plans)
 * - When mc_status = '1' and mc_type = '2', user gets 'member' status
 * - Treated similar to regular members but may have different billing arrangements
 * - Often used for accounts with higher site limits or custom configurations
 */

const mysql = require('../utils/db')

const { phpSerializeQuery, isEmailValid } = require('../utils/common')
const {redisClient} = require('../utils/redis-client.js');
const BRANCH = process.env?.K8S_BRANCH || 'local';

const updateVendorSitesChargifyOld = async (vendorId) => {
    try {
        //only run this on master
       
        if(BRANCH !== 'master') {
            return;
        }
        // Get current active sites count
        let [chnres] = await mysql.awaitQuery(
            `SELECT COUNT(mb_id) AS total 
             FROM maptile_building 
             WHERE mb_user_type = '1' AND mb_user_id = ? AND mb_status = '1'`, 
            [vendorId]
        );

        if (chnres) {
            const currentActiveSites = chnres.total;
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1; 
            const currentYear = currentDate.getFullYear();

            // Get current Chargify record
            let [maxr] = await mysql.awaitQuery(
                `SELECT * FROM maptile_chargify WHERE mc_vendor = ?`, 
                [vendorId]
            );
           
            if (maxr) {
                const row = maxr;
                const storedMaxSites = row.mc_max;
                const lastUpdateMonth = row.mc_last_update_month;
                const lastUpdateYear = row.mc_last_update_year;

                // Update max sites if it's a new month or if current active sites is greater
                if (currentMonth != lastUpdateMonth || currentYear != lastUpdateYear || currentActiveSites > storedMaxSites) {
                    const newMaxSites = (currentMonth != lastUpdateMonth || currentYear != lastUpdateYear) 
                        ? currentActiveSites 
                        : Math.max(currentActiveSites, storedMaxSites);

                    await mysql.awaitQuery(
                        `UPDATE maptile_chargify 
                         SET mc_max = ?, mc_last_update_month = ?, mc_last_update_year = ? 
                         WHERE mc_vendor = ?`,
                        [newMaxSites, currentMonth, currentYear, vendorId]
                    );
                   
                    // If it's a new billing cycle and we need to update Chargify
                    if (row.mc_fixed == '0' && newMaxSites > storedMaxSites && row.mc_sub) {
                      
                        let params = {
                            allocation: {
                                'proration_upgrade_scheme': "no-prorate",
                                'proration_downgrade_scheme': "no-prorate",
                                'quantity': newMaxSites ? (isNaN(parseInt(newMaxSites)) ? 10 : (parseInt(newMaxSites) % 10 === 0 ? parseInt(newMaxSites) : Math.ceil(parseInt(newMaxSites) / 10) * 10)) : 10,
                                'memo': "Max Buildings increased."
                            }
                        }
                        let prd = row.mc_per != '0' ? row.mc_per : 63303 //static $chargify['main_prod'] from PHP
                        let url = `https://${process.env.CHARGIFY_SUB_DOMAIN}.chargify.com/subscriptions/${row.mc_sub}/components/${prd}/allocations.json`
                        let username = `${process.env.CHARGIFY_API_KEY}`
                        let request = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'Authorization': 'Basic ' + btoa(`${username}:x`),
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(params)
                        })
                        if(!request.ok) {
                            console.error("Chargify Error", vendorId)
                            console.error(params.allocation)
                            console.error(request)
                            console.error(await request.text())
                        }
                    }
                }
            }
        }
    } catch (e) {
        console.error("Chargify Error", e);
    }
}

const syncFleetServer = async (vendorId) => {
    let vRes = await mysql.awaitQuery(`SELECT vendor_access_code FROM maptile_vendors WHERE vendor_id =  ?`, [vendorId])
    if (vRes && vRes[0]) {
        const accessCode = vRes[0]['vendor_access_code']
        let bRes = await mysql.awaitQuery(`Select mb_id, mb_nickname,mb_geo,mb_zip_code,mb_contractor_vid,mb_last_modified From maptile_building where mb_user_id = ? and mb_user_type = '1' AND mb_status = '1' ORDER BY mb_nickname`, [vendorId])
        if (process.env.K8S_BRANCH && process.env.K8S_BRANCH === "master") {
            if (bRes && bRes[0]) {
                let req = {
                    'accessCode': accessCode,
                    'vid': vendorId,
                    'buildings': bRes
                }
                const response = await fetch('https://fleet.sitefotos.com/updatebuildings', {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    method: 'POST',
                    body: phpSerializeQuery(req)
                });
            }
        }
    }
}

const checkSiteLimit = async (vendorId) => {
    let queryBuildingLimit = await mysql.awaitQuery(`SELECT * from maptile_chargify where mc_vendor=?`, [vendorId])
    let obj = {
        "error": false,
        "message": "",
        "detail": ""
    }

    if (queryBuildingLimit && queryBuildingLimit.length !== 0) {
        if (queryBuildingLimit[0]['mc_type'] == '1' && queryBuildingLimit[0]['mc_max'] >= 5) {
            obj.error = true;
            obj.message = "max";
            obj.detail = "Max site limit exceeded"
            return obj;
        } else if (queryBuildingLimit[0]['mc_type'] == '0' && queryBuildingLimit[0]['mc_pastdue'] == '1') {
            obj.error = true;
            obj.message = "past";
            obj.detail = "Account is past due"
            return obj;
        } else if (queryBuildingLimit[0]['mc_type'] == '0' && queryBuildingLimit[0]['mc_status'] == '0') {
            obj.error = true;
            obj.message = "join";
            obj.detail = "Active subscription required"
            return obj;
        }
        else {
            return obj;
        }
    }
    else {
        obj.error = true;
        obj.message = "join";
        obj.detail = "Active subscription required"
        return obj
    }
}

const replaceSiteSharingEmails = async (siteId, vendorId, emails) => {
    if (emails) {
        if (!Array.isArray(emails))
            emails = emails.toSring().split(',')
        await mysql.deleteObj('maptile_building_share', ['mbs_building', 'mbs_owner'], [siteId, vendorId])
        for (let emailAddress of emails) {
            if (isEmailValid(emailAddress)) {
                emailAddress = emailAddress.toLowerCase().trim()
                let vRes = await mysql.awaitQuery(`SELECT vendor_id FROM maptile_vendors where vendor_email LIKE '?'`, [emailAddress])
                if (vRes && vRes[0]) {
                    let sVendor = vRes[0]['vendor_id']
                    let mbsRes = await mysql.awaitQuery(`SELECT mbs_id from maptile_building_share WHERE mbs_user=? AND mbs_owner =? AND mbs_building = ?`, [sVendor, vendorId, siteId])
                    if (!mbsRes[0]) {
                        let data = {
                            'mbs_building': siteId,
                            'mbs_owner': vendorId,
                            'mbs_user': sVendor
                        }
                        await mysql.insertObj('maptile_building_share', data)
                    }
                }
                else {
                    //TODO after adding vendor related functionality to node.
                    /* 
                    let nVid = createNewVendor(emailAddress);
                    
                    let data = {
                        'mbs_building': siteId,
                        'mbs_owner': vendorId,
                        'mbs_user': nVid
                    }
                    await mysql.insertObj('maptile_building_share', data) 
                    */
                }

            }
        }

    }
}

const fetchChargifySubscription = async (subscriptionId) => {
    try {
        const url = `https://${process.env.CHARGIFY_SUB_DOMAIN}.chargify.com/subscriptions/${subscriptionId}.json`;
        const username = `${process.env.CHARGIFY_API_KEY}`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': 'Basic ' + btoa(`${username}:x`),
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error("Chargify Error", await response.text());
            return null;
        }

        const { subscription } = await response.json();
        return subscription;
    } catch (e) {
        console.error("Failed to fetch subscription details:", e);
        return null;
    }
};
const updateVendorSitesChargify = async (vendorId) => {
    try {
        if (BRANCH !== 'master') {
            console.log("Not on master branch. Exiting.");
            return;
        }

        const [chnres] = await mysql.awaitQuery(
            `SELECT COUNT(mb_id) AS total 
             FROM maptile_building 
             WHERE mb_user_type = '1' AND mb_user_id = ? AND mb_status in ('1', '2')`,
            [vendorId]
        );

        if (chnres) {
            const currentActiveSites = chnres.total;
            console.log(`Current active sites: ${currentActiveSites}`);

            const [vendorRecord] = await mysql.awaitQuery(
                `SELECT * FROM maptile_chargify WHERE mc_vendor = ?`,
                [vendorId]
            );

            if (vendorRecord) {
                let {
                    mc_max: storedMaxSites,
                    mc_last_update_month: lastUpdateMonth,
                    mc_last_update_year: lastUpdateYear,
                    mc_billing_start_date,
                    mc_fixed,
                    mc_sub,
                    mc_per
                } = vendorRecord;

                let subscription = null;
                let billingStartDate = mc_billing_start_date ? new Date(mc_billing_start_date) : null;
                let currentBillingPeriodStart = null, currentBillingPeriodEnd = null;
                
                if(!mc_sub) {
                    console.log("No subscription ID (mc_sub) found.");
                    await mysql.updateObj('maptile_chargify', 
                        { mc_max: currentActiveSites },
                        ['mc_vendor'],
                        [vendorId]
                    );
                    return;
                }

                if (!billingStartDate) {
                    subscription = await fetchChargifySubscription(mc_sub);

                    if (subscription) {
                        billingStartDate = new Date(subscription.activated_at);
                        currentBillingPeriodStart = new Date(subscription.current_period_started_at);
                        currentBillingPeriodEnd = new Date(subscription.current_period_ends_at);

                        await mysql.updateObj('maptile_chargify',
                            { mc_billing_start_date: billingStartDate.toISOString() },
                            ['mc_vendor'],
                            [vendorId]
                        );
                    } else {
                        console.error("Unable to fetch Chargify subscription details.");
                        return;
                    }
                } else {
                    subscription = await fetchChargifySubscription(mc_sub);
                    currentBillingPeriodStart = new Date(subscription.current_period_started_at);
                    currentBillingPeriodEnd = new Date(subscription.current_period_ends_at);
                }

                const currentDate = new Date();
                const isNewBillingPeriod = currentDate >= currentBillingPeriodStart && currentDate <= currentBillingPeriodEnd && 
                                         (lastUpdateMonth !== currentBillingPeriodStart.getMonth() + 1 || lastUpdateYear !== currentBillingPeriodStart.getFullYear());

                const newMaxSites = isNewBillingPeriod 
                    ? currentActiveSites
                    : Math.max(currentActiveSites, storedMaxSites);

                if (newMaxSites > storedMaxSites || isNewBillingPeriod) {
                    await mysql.updateObj('maptile_chargify',
                        {
                            mc_max: newMaxSites,
                            mc_last_update_month: currentBillingPeriodStart.getMonth() + 1,
                            mc_last_update_year: currentBillingPeriodStart.getFullYear()
                        },
                        ['mc_vendor'],
                        [vendorId]
                    );

                    if (mc_fixed == '0' && mc_sub) { // Check mc_fixed is '0' (string) and mc_sub exists
                        let shouldUpdateChargifyAPI = false;
                        let chargifyMemo = "";

                        if (isNewBillingPeriod) {
                            shouldUpdateChargifyAPI = true;
                            chargifyMemo = `Billing period updated. Site quantity reflects current usage of ${currentActiveSites}.`;
                        } else if (newMaxSites > storedMaxSites) {
                            shouldUpdateChargifyAPI = true;
                            chargifyMemo = `Max sites for billing period increased to ${newMaxSites}.`;
                        }

                        if (shouldUpdateChargifyAPI) {
                            console.log(`Vendor ID: ${vendorId} | Preparing to update Chargify. Reason: ${chargifyMemo}`);
                            const parsedNewMaxSites = parseInt(newMaxSites, 10);
                            const quantity = isNaN(parsedNewMaxSites) ? 10 : Math.ceil(parsedNewMaxSites / 10) * 10;

                            const params = {
                                allocation: {
                                    'proration_upgrade_scheme': "no-prorate",
                                    'proration_downgrade_scheme': "no-prorate",
                                    'quantity': quantity,
                                    'memo': chargifyMemo
                                }
                            };

                            const prd = mc_per != '0' ? mc_per : 63303;
                            const url = `https://${process.env.CHARGIFY_SUB_DOMAIN}.chargify.com/subscriptions/${mc_sub}/components/${prd}/allocations.json`;
                            const username = `${process.env.CHARGIFY_API_KEY}`;

                            console.log(`Vendor ID: ${vendorId} | Chargify URL: ${url} | Params: ${JSON.stringify(params)}`);

                            const request = await fetch(url, {
                                method: 'POST',
                                headers: {
                                    'Authorization': 'Basic ' + btoa(`${username}:x`),
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(params)
                            });

                            if (!request.ok) {
                                console.error("Chargify API Error for vendor", vendorId);
                                console.error("Allocation Details:", params.allocation);
                                console.error("Request Response:", await request.text());
                            } else {
                                console.log("Chargify updated successfully for vendor", vendorId);
                            }
                        } else {
                            console.log(`Vendor ID: ${vendorId} | No API update to Chargify needed. isNewBillingPeriod: ${isNewBillingPeriod}, newMaxSites: ${newMaxSites}, storedMaxSites: ${storedMaxSites}`);
                        }
                    } else {
                        if (mc_fixed != '0') {
                            console.log(`Vendor ID: ${vendorId} | Plan is fixed (mc_fixed: ${mc_fixed}). No Chargify API update.`);
                        }
                        if (!mc_sub) {
                            console.log(`Vendor ID: ${vendorId} | No Chargify subscription ID (mc_sub). No Chargify API update.`);
                        }
                    }
                }
            }
        }
    } catch (e) {
        console.error("Chargify Error", e);
    }
};




module.exports = {
    updateVendorSitesChargify,
    syncFleetServer,
    replaceSiteSharingEmails,
    checkSiteLimit
}