#provider-switch {

    height: fit-content;
    width: fit-content;

    position: absolute;
    margin-left:10px;
    left: 0px;
    bottom: 0px;
    overflow-x: hidden;
    padding:1px;
}

#provider-switch .v-btn {
	height: 40px;

}


#mapcontainer div.leaflet-bottom.leaflet-right {
    position:fixed;
}

#map-sidebar .v-expansion-panel::before{
   box-shadow:  0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)!important;
}


.layer-manager-color-picker .v-color-picker__controls{
display:none;
}
.color-swatch-button {
    cursor: pointer;
    height: 30px;
    width: 30px;
}

.text-swatch-button {
    cursor: pointer;
    height: 25px;
    width: 25px;
    margin-top:6px;
    border: 1px solid black;
}

.text-decoration-underline-dotted {    
    border-bottom: 1px dotted #000;
    text-decoration: none;
}
.disabled-map {
    pointer-events: none!important;
}
.disabled-map .leaflet-control {
    pointer-events: none!important;
}
.show-btns {
    color: rgba(255, 255, 255, 1) !important;
}
#print-control-launcher {


	width: 32px;
    height: 32px;
    line-height: 32px;
    overflow: hidden;
    padding:1px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff;

}
.leaflet-div-icon5 {
    background: white;
    border: 5px solid rgba(255, 255, 255, 0.5);
    color: black;
    font-weight: bold;
    font-size: 25px;
    text-align: center;
    border-radius: 50%;
    line-height: 30px;
}
#map-view-control-launcher {

	width: 32px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    margin-left:10px;
    left: 0px;
    bottom: 60px;
    overflow: hidden;
    padding:1px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff;

}
#map-view-card {


    position: absolute;
    left: 10px;
    bottom: 0px;
    overflow: hidden;
    padding: 1px;

}

.normal-spacing {
    letter-spacing: normal!important;
}
.stop-caps
{
    text-transform: none!important;
}

#map-page .leaflet-bottom.leaflet-left {
    margin-bottom: 40px;
}
.leaflet-control-attribution {
    font-size: 11px;
    background: rgba(255, 255, 255, 0.7);
    margin-right:0px!important;
    margin-bottom:0px!important;
}
#highres-date {
    display: inline-block;
    margin-left: 6px;
    background: #FF6600;
    height: 26px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 2px;
    font-weight: 600;
}

.highres-dialog-tables td {
    font-size: .75rem!important;
    padding:0!important;
}
.highres-dialog-tables th {
    padding:0!important;
}
#item-prop-popup {
    position: absolute;
    margin: 10px;
    border: 1px solid rgb(0, 0, 0);
    border-radius: 10px;
    background-color: rgb(255, 255, 255) !important;
    font-family: Roboto, Arial, sans-serif;
    width: 270px !important;
    padding: 1px !important;
}

#item-prop-popup.no-pseudo-before:before {
    display: none;
}

#item-prop-popup.no-pseudo-after:after {
    display: none;
}
#sla-table table {
    overflow-x: scroll;
    min-width: 900px;
}

#allocation-table table {
    overflow-x: scroll;
    min-width: 900px;
}

#item-prop-popup:before {
    content: '';
    position: absolute;
    top: 0%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    width: 0;
    height: 0;
    border-bottom: solid 10px white;
    border-left: solid 10px transparent;
    border-right: solid 10px transparent;
}
.content-editable-text:hover:before {
    content: '✎';
    position: absolute;
    left: -2px;
}


.content-editable-box:focus {
    border: 1px solid #ddd;
    padding: 4px;
}
.allTextarea {
    color: white;
    line-height: 1.1;
    text-shadow: -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
    pointer-events: none!important;
}
.allTextAreaSpan {
    pointer-events: all;
    cursor:pointer;
}
#item-prop-popup:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -10px;
    width: 0;
    height: 0;
    border-top: solid 10px white;
    border-left: solid 10px transparent;
    border-right: solid 10px transparent;
}
.leaflet-marker-icon .number {
    position: relative;
    top: -45px;
    font-size: 12px;
    text-align: center;
}
#notch-icon {
    background-color: #ff6600;
    width:24px;
    top: 0%;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
    cursor:pointer;
    left: 0%;
    font-size:24px;
    padding-left:8px;
    padding-right:10px;
    font-stretch: 100%;
    font-weight:600;
    color:#ffffff;
    margin-left: -24px;
    position: absolute;
}
.stopcaps {
    text-transform: none !important;
}
html { overflow-y: auto!important }
.leaflet-edit-marker-selected{background-color:rgba(254,87,161,0.1);border:4px dashed rgba(254,87,161,0.6);-webkit-border-radius:4px;border-radius:4px;box-sizing:content-box}.leaflet-edit-move{cursor:move}.leaflet-edit-resize{cursor:pointer}.leaflet-oldie .leaflet-draw-toolbar{border:1px solid #999}
.leaflet-legend-control {
    background: #FFF;
    border: 2px solid #FFF;
    padding: 6px;
    max-height: 250px;
    overflow-y: auto;
    line-height: 1.2;
    overflow-x: hidden;
}

.leaflet-legend-control .legend-symbol {
    margin-right: 6px;
    display: inline-flex;
    vertical-align: middle;
}

.leaflet-legend-control .legend-label {
    max-width: 200px;
    vertical-align: middle;
    word-wrap: break-word;
    color: #555;
    font-size: 14px;
    font-weight: 400;
}

.csssquare {
    width: 22px;
    height: 22px;
    border-style: solid;
    border-width: 1px;
}

.leaflet-legend-control .legend-block {
    position: relative;
    margin-right: 7px;
    margin-top: 2px;
    width: 100%;
}

#map-page #group-panel {
    height: initial;
    top: 12px!important;
    max-height: 400px;
    transform: translateX(0%);
    //width: 256px;
    width: 300px;
    overflow: initial;
    position: relative;
    float: right;
    clear: both;
}


#map-page #map-launcher {
    position: relative;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
    top: 5px;
    right: 2px;
    float: right;
}

#map-page #map-launcher {
    position: relative;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
    top: 10px;
}

.leaflet-control-zoom-in, .leaflet-control-zoom-out {
    font: bold 24px 'Lucida Console', Monaco, monospace;
    text-indent: 1px;
}


.v-expansion-panel--active>.v-expansion-panel-header {
    min-height: inherit!important; ;
}
.v-expansion-panel-header {
    min-height: inherit!important;

}
.v-expansion-panels {
    border-radius: 0px!important;
 }
 .v-expansion-panels>:last-child {
     border-bottom-left-radius: 0px!important;
     border-bottom-right-radius: 0px!important;
}

.v-expansion-panels>:first-child {
     border-top-left-radius: 0px!important;
     border-top-right-radius: 0px!important;
}

.v-card>:last-child:not(.v-btn):not(.v-chip) {
         border-bottom-left-radius: 0px!important;
     border-bottom-right-radius: 0px!important;
}

#map-page .cell-icon {
    height: auto;
    width: auto;
    max-width: 50px;
    display: inline-block;
    margin: auto;
}

#map-page  .leaflet-bar a, .leaflet-bar a:hover {
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    width: 32px;
    height: 32px;
    line-height: 32px;
    display: block;
    text-align: center;
    text-decoration: none;
    color: #000;
}

#map-page .leaflet-pm-action
{
    width: auto!important;
    background-color: rgb(102, 102, 102)!important;
    color: #fff!important;
    border-bottom: none!important;
}
#map-page .leaflet-pm-action:hover
{
    background-color: #777!important;
}

/*
#map-page  .button-container >:first-child{
     border-top-left-radius: 6px!important;
     border-top-right-radius: 6px!important;

}
*/
#map-page  .leafelet-pm-draw >:first-child{
     border-top-left-radius: 6px!important;
     border-top-right-radius: 6px!important;

}



#map-view-control-launcher {
	   width: 32px;
    height: 32px;
    line-height: 32px;

}



